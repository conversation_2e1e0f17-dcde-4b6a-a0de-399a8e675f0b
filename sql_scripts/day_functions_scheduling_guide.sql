-- Day Functions Scheduling Guide
-- These functions are designed to run repeatedly during business hours (8 AM - 5 PM Pacific Time)
-- IMPORTANT: Functions have built-in Pacific Time validation and will reject execution outside business hours

-- RECOMMENDED SCHEDULING FREQUENCIES:

-- 1. HIGH FREQUENCY (Every 15 minutes) - For peak processing
-- Run every 15 minutes from 8 AM to 5 PM PT with small batch sizes
-- Example cron: */15 8-16 * * * TZ=America/Los_Angeles psql -d your_db -c "SELECT build_day_vz_optimized('journey_name', 'platform_name', 250);"

-- 2. MEDIUM FREQUENCY (Every 30 minutes) - For balanced processing
-- Run every 30 minutes from 8 AM to 5 PM PT with medium batch sizes
-- Example cron: */30 8-16 * * * TZ=America/Los_Angeles psql -d your_db -c "SELECT build_day_vz_optimized('journey_name', 'platform_name', 500);"

-- 3. LOW FREQUENCY (Every hour) - For light processing
-- Run every hour from 8 AM to 5 PM PT with larger batch sizes
-- Example cron: 0 8-16 * * * TZ=America/Los_Angeles psql -d your_db -c "SELECT build_day_vz_optimized('journey_name', 'platform_name', 1000);"

-- SAMPLE CRON CONFIGURATIONS:

-- Day Verizon Processing (every 20 minutes during business hours PT)
-- 0,20,40 8-16 * * * TZ=America/Los_Angeles psql -h your_host -d your_db -U your_user -c "SELECT build_day_vz_optimized('your_journey', 'your_platform', 400);"

-- Day Non-Verizon Processing (every 25 minutes during business hours PT)
-- 5,30,55 8-16 * * * TZ=America/Los_Angeles psql -h your_host -d your_db -U your_user -c "SELECT build_day_nv_optimized('your_journey', 'your_platform', 400);"

-- MONITORING QUERIES:

-- Check current processing window and eligible records
SELECT * FROM subscriber.sms_processing_stats WHERE function_name LIKE 'day_%';

-- Check recent transaction activity
SELECT 
    platform,
    COUNT(*) as transactions_today,
    MAX(id_sms) as latest_transaction_id,
    COUNT(DISTINCT phone) as unique_phones
FROM subscriber.transaction_sms 
WHERE campaign_date = CURRENT_DATE 
  AND platform IN ('mce_vz', 'mce_fcl', 'mce_rto', 'mce_crc')
GROUP BY platform
ORDER BY transactions_today DESC;

-- Check processing performance over time
SELECT 
    DATE_TRUNC('hour', delivery_date) as hour_bucket,
    platform,
    COUNT(*) as messages_sent
FROM subscriber.transaction_sms 
WHERE campaign_date = CURRENT_DATE 
  AND delivery_date IS NOT NULL
GROUP BY DATE_TRUNC('hour', delivery_date), platform
ORDER BY hour_bucket DESC, messages_sent DESC;

-- PERFORMANCE TUNING RECOMMENDATIONS:

-- 1. Batch Size Guidelines:
--    - Start with 250-500 records per batch for day functions
--    - Monitor system performance and adjust accordingly
--    - Smaller batches = more frequent processing, less system impact
--    - Larger batches = fewer executions, more efficient but higher system load

-- 2. Frequency Guidelines:
--    - High-volume periods: Every 15-20 minutes with smaller batches
--    - Normal periods: Every 30 minutes with medium batches  
--    - Low-volume periods: Every hour with larger batches

-- 3. System Load Considerations:
--    - Day functions have shorter delays (0.05s vs 0.1s) between batches
--    - Built-in business hours check (8 AM - 5 PM) prevents off-hours execution
--    - Advisory locks prevent multiple instances from running simultaneously

-- EXAMPLE IMPLEMENTATION:

-- Create a simple wrapper script for easier cron management
/*
#!/bin/bash
# day_vz_processor.sh

DB_HOST="your_host"
DB_NAME="your_db" 
DB_USER="your_user"
JOURNEY="your_journey"
PLATFORM="your_platform"
BATCH_SIZE=400

# Log file for monitoring
LOG_FILE="/var/log/sms_processing/day_vz_$(date +%Y%m%d).log"

# Execute the function and log results
echo "$(date): Starting Day VZ processing..." >> $LOG_FILE
RESULT=$(psql -h $DB_HOST -d $DB_NAME -U $DB_USER -t -c "SELECT build_day_vz_optimized('$JOURNEY', '$PLATFORM', $BATCH_SIZE);")
echo "$(date): $RESULT" >> $LOG_FILE

# Optional: Send alerts if errors occur
if [[ $RESULT == *"Error"* ]]; then
    echo "Day VZ processing error: $RESULT" | mail -s "SMS Processing Alert" <EMAIL>
fi
*/

-- Then in crontab:
-- */20 8-16 * * * /path/to/day_vz_processor.sh
-- */25 8-16 * * * /path/to/day_nv_processor.sh
