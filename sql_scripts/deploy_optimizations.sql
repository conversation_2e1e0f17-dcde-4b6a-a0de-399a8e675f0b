-- Deployment script for SMS function optimizations
-- Execute these steps in order

-- Step 1: Create performance indexes (run during low-traffic period)
\echo 'Creating performance indexes...'
\i sql_scripts/create_performance_indexes.sql

-- Step 2: Create optimized functions
\echo 'Creating optimized functions...'
\i sql_scripts/build_night_vz_optimized.sql
\i sql_scripts/build_night_nv_optimized.sql
\i sql_scripts/build_day_vz_optimized.sql
\i sql_scripts/build_day_nv_optimized.sql

-- Step 3: Test the optimized functions with small batch size first
\echo 'Testing optimized functions...'
SELECT build_night_vz_optimized('test_journey', 'test_platform', 100);
SELECT build_night_nv_optimized('test_journey', 'test_platform', 100);
SELECT build_day_vz_optimized('test_journey', 'test_platform', 50);
SELECT build_day_nv_optimized('test_journey', 'test_platform', 50);

-- Step 4: Create comprehensive monitoring view for all functions
CREATE OR REPLACE VIEW subscriber.sms_processing_stats AS
-- Night VZ (Verizon night processing)
SELECT
    'night_vz' as function_name,
    'Night Verizon Processing' as description,
    COUNT(*) as eligible_records,
    MIN(leaddate) as earliest_lead,
    MAX(leaddate) as latest_lead,
    EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) as current_hour_pt,
    CASE
        WHEN EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) BETWEEN 22 AND 23
             OR EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) BETWEEN 0 AND 7
        THEN 'ACTIVE_WINDOW'
        ELSE 'INACTIVE_WINDOW'
    END as processing_window
FROM subscriber.subscriber_sms s
LEFT JOIN subscriber.brand b ON s.brand_id = b.brand_id
WHERE s.leaddate >= date_trunc('day', now()) - interval '1 day' + interval '10 hours'
  AND s.leaddate <= date_trunc('day', now()) + interval '1 hours'
  AND s.blacklisted <> 'true'
  AND s.carrierparent LIKE '%VERIZON%'
  AND s.status <> 'not_mobile'
  AND s.adid NOT LIKE 'CCS440811%'
  AND s.original_adid NOT LIKE 'CCS440811%'
  AND s.shorturl IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM subscriber.optout_sms o WHERE o.phone = s.customer_phone)
  AND NOT EXISTS (SELECT 1 FROM subscriber.transaction_sms t WHERE t.phone = s.customer_phone AND t.campaign_date = CURRENT_DATE)

UNION ALL

-- Night NV (Non-Verizon night processing)
SELECT
    'night_nv' as function_name,
    'Night Non-Verizon Processing' as description,
    COUNT(*) as eligible_records,
    MIN(leaddate) as earliest_lead,
    MAX(leaddate) as latest_lead,
    EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) as current_hour_pt,
    CASE
        WHEN EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) BETWEEN 22 AND 23
             OR EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) BETWEEN 0 AND 7
        THEN 'ACTIVE_WINDOW'
        ELSE 'INACTIVE_WINDOW'
    END as processing_window
FROM subscriber.subscriber_sms s
LEFT JOIN subscriber.brand b ON s.brand_id = b.brand_id
WHERE s.leaddate >= date_trunc('day', now()) - interval '1 day' + interval '10 hours'
  AND s.leaddate <= date_trunc('day', now()) + interval '1 hours'
  AND s.blacklisted <> 'true'
  AND s.carrierparent NOT LIKE '%VERIZON%'
  AND s.status <> 'not_mobile'
  AND s.adid NOT LIKE 'CCS440811%'
  AND s.original_adid NOT LIKE 'CCS440811%'
  AND s.shorturl IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM subscriber.optout_sms o WHERE o.phone = s.customer_phone)
  AND NOT EXISTS (SELECT 1 FROM subscriber.transaction_sms t WHERE t.phone = s.customer_phone AND t.campaign_date = CURRENT_DATE)

UNION ALL

-- Day VZ (Verizon day processing)
SELECT
    'day_vz' as function_name,
    'Day Verizon Processing' as description,
    COUNT(*) as eligible_records,
    MIN(leaddate) as earliest_lead,
    MAX(leaddate) as latest_lead,
    EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) as current_hour_pt,
    CASE
        WHEN EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) BETWEEN 8 AND 16
        THEN 'ACTIVE_WINDOW'
        ELSE 'INACTIVE_WINDOW'
    END as processing_window
FROM subscriber.subscriber_sms s
LEFT JOIN subscriber.brand b ON s.brand_id = b.brand_id
WHERE s.leaddate >= date_trunc('day', now()) + interval '1 hours'
  AND s.leaddate <= date_trunc('day', now()) + interval '10 hours'
  AND s.blacklisted <> 'true'
  AND s.carrierparent LIKE '%VERIZON%'
  AND s.status <> 'not_mobile'
  AND s.adid NOT LIKE 'CCS440811%'
  AND s.original_adid NOT LIKE 'CCS440811%'
  AND s.shorturl IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM subscriber.optout_sms o WHERE o.phone = s.customer_phone)
  AND NOT EXISTS (SELECT 1 FROM subscriber.transaction_sms t WHERE t.phone = s.customer_phone AND t.campaign_date = CURRENT_DATE)

UNION ALL

-- Day NV (Non-Verizon day processing)
SELECT
    'day_nv' as function_name,
    'Day Non-Verizon Processing' as description,
    COUNT(*) as eligible_records,
    MIN(leaddate) as earliest_lead,
    MAX(leaddate) as latest_lead,
    EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) as current_hour_pt,
    CASE
        WHEN EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles')) BETWEEN 8 AND 16
        THEN 'ACTIVE_WINDOW'
        ELSE 'INACTIVE_WINDOW'
    END as processing_window
FROM subscriber.subscriber_sms s
LEFT JOIN subscriber.brand b ON s.brand_id = b.brand_id
WHERE s.leaddate >= date_trunc('day', now()) + interval '1 hours'
  AND s.leaddate <= date_trunc('day', now()) + interval '10 hours'
  AND s.blacklisted <> 'true'
  AND s.carrierparent NOT LIKE '%VERIZON%'
  AND s.status <> 'not_mobile'
  AND s.adid NOT LIKE 'CCS440811%'
  AND s.original_adid NOT LIKE 'CCS440811%'
  AND s.shorturl IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM subscriber.optout_sms o WHERE o.phone = s.customer_phone)
  AND NOT EXISTS (SELECT 1 FROM subscriber.transaction_sms t WHERE t.phone = s.customer_phone AND t.campaign_date = CURRENT_DATE);

\echo 'Optimization deployment complete!'
\echo 'Check processing stats with: SELECT * FROM subscriber.sms_processing_stats;'
