-- Performance indexes for build_night_vz and build_day_nv functions
-- Run these indexes BEFORE running the optimized functions

-- Critical composite index for subscriber_sms main query
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriber_sms_leaddate_carrier_status 
ON subscriber.subscriber_sms (leaddate, carrierparent, blacklisted, status, shorturl);

-- Index for brand lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriber_sms_brand_id 
ON subscriber.subscriber_sms (brand_id);

-- Index for optout_sms lookups (phone is already primary key, but ensure it's optimized)
-- Phone is already PRIMARY KEY, so this is covered

-- Index for transaction_sms duplicate check
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_transaction_sms_phone_campaign_date 
ON subscriber.transaction_sms (phone, campaign_date);

-- Index for content_sms lookups
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_content_sms_vertical_journey 
ON subscriber.content_sms (vertical, journey);

-- Index for zip lookups (zip is already primary key)
-- Zip is already PRIMARY KEY, so this is covered

-- Additional indexes for specific filter conditions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriber_sms_adid 
ON subscriber.subscriber_sms (adid);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriber_sms_original_adid 
ON subscriber.subscriber_sms (original_adid);

-- Partial indexes for better performance on specific conditions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriber_sms_verizon_active 
ON subscriber.subscriber_sms (leaddate, customer_phone, brand_id) 
WHERE carrierparent LIKE '%VERIZON%' 
  AND blacklisted <> 'true' 
  AND status <> 'not_mobile' 
  AND shorturl IS NOT NULL
  AND adid NOT LIKE 'CCS440811%'
  AND original_adid NOT LIKE 'CCS440811%';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_subscriber_sms_non_verizon_active 
ON subscriber.subscriber_sms (leaddate, customer_phone, brand_id) 
WHERE carrierparent NOT LIKE '%VERIZON%' 
  AND blacklisted <> 'true' 
  AND status <> 'not_mobile' 
  AND shorturl IS NOT NULL
  AND adid NOT LIKE 'CCS440811%'
  AND original_adid NOT LIKE 'CCS440811%';
