CREATE OR REPLACE FUNCTION build_day_nv(
    p_journey subscriber.content_sms.journey%TYPE,
    p_platform subscriber.transaction_sms.platform%TYPE,
    p_batch_size INT DEFAULT 100
)
RETURNS TEXT AS
$$
DECLARE
    v_lock_acquired BOOLEAN := FALSE;
    v_lock_id BIGINT := hashtext('build_day_nv');
    v_total_count INT := 0;
    v_batch_count INT;
    v_last_processed_phone VARCHAR(255) := '';
    v_start_time TIMESTAMP := clock_timestamp();
    v_batch_start_time TIMESTAMP;
    v_current_hour INT := EXTRACT(HOUR FROM (NOW() AT TIME ZONE 'America/Los_Angeles'));
BEGIN
    -- Check if we're within business hours (8 AM to 5 PM Pacific Time)
    IF v_current_hour < 8 OR v_current_hour >= 17 THEN
        RETURN FORMAT('Function build_day_nv can only run during business hours (8 AM - 5 PM PT). Current PT hour: %s', v_current_hour);
    END IF;

    -- Try to acquire an advisory lock (non-blocking)
    SELECT pg_try_advisory_lock(v_lock_id) INTO v_lock_acquired;

    IF NOT v_lock_acquired THEN
        RETURN 'Function build_day_nv is already running. Exiting to prevent duplicate execution.';
    END IF;

    -- Process in batches to avoid long-running transactions
    LOOP
        v_batch_start_time := clock_timestamp();
        
        -- Insert batch with optimized query using cursor-style pagination
        -- This ensures we don't skip unprocessed records due to OFFSET issues
        WITH eligible_subscribers AS (
            SELECT
                s.customer_phone,
                s.emailaddress,
                s.firstname,
                s.lastname,
                CASE
                    WHEN b.vertical = 'CRC' AND s.pl_shorturl IS NOT NULL
                    THEN s.pl_shorturl
                    ELSE s.shorturl
                END as shorturl,
                s.zip,
                b.brand_name,
                b.vertical
            FROM subscriber.subscriber_sms s
            LEFT JOIN subscriber.brand b ON s.brand_id = b.brand_id
            WHERE s.leaddate >= date_trunc('day', now()) + interval '1 hours'
              AND s.leaddate <= date_trunc('day', now()) + interval '10 hours'
              AND s.blacklisted <> 'true'
              AND s.carrierparent NOT LIKE '%VERIZON%'
              AND s.status <> 'not_mobile'
              AND s.adid NOT LIKE 'CCS440811%'
              AND s.original_adid NOT LIKE 'CCS440811%'
              AND s.shorturl IS NOT NULL
              AND s.customer_phone > v_last_processed_phone  -- Cursor-style pagination
              AND NOT EXISTS (
                  SELECT 1 FROM subscriber.optout_sms o
                  WHERE o.phone = s.customer_phone
              )
              AND NOT EXISTS (
                  SELECT 1 FROM subscriber.transaction_sms t
                  WHERE t.phone = s.customer_phone
                    AND t.campaign_date = CURRENT_DATE
              )
            ORDER BY s.customer_phone
            LIMIT p_batch_size
        ),
        message_data AS (
            SELECT 
                es.*,
                scs.version,
                scs.message,
                z.prop_count,
                z.city
            FROM eligible_subscribers es
            LEFT JOIN subscriber.content_sms scs ON scs.vertical = es.vertical 
                AND scs.journey = p_journey
            LEFT JOIN subscriber.zip z ON z.zip = es.zip
        ),
        final_messages AS (
            SELECT 
                customer_phone,
                emailaddress,
                firstname,
                lastname,
                version,
                CONCAT(
                    REPLACE(
                        REPLACE(
                            REPLACE(
                                REPLACE(
                                    REPLACE(
                                        REPLACE(
                                            REPLACE(
                                                REPLACE(
                                                    REPLACE(message, '{{firstname}}', firstname),
                                                    '{{lastname}}', lastname
                                                ),
                                                '{{email}}', emailaddress
                                            ),
                                            '{{zip}}', zip
                                        ),
                                        '{{prop_count}}', 
                                        CASE WHEN prop_count = 0 THEN 'newly available' ELSE prop_count::TEXT END
                                    ),
                                    '{{city}}', city
                                ),
                                '{{brand_name}}', brand_name
                            ),
                            '{{weekday}}', to_char(CURRENT_DATE, 'Day')
                        ),
                        '{{date}}', to_char(CURRENT_DATE, 'MM/DD/YYYY')
                    ),
                    ' ', shorturl, '?A=', version, '&subid4=', 
                    to_hex(CAST(EXTRACT(EPOCH FROM CURRENT_DATE) AS INTEGER)), 
                    ' Reply STOP to unsubscribe.'
                ) as final_message,
                CASE
                    WHEN p_platform = 'mce' AND vertical = 'FCL' THEN 'mce_fcl'
                    WHEN p_platform = 'mce' AND vertical = 'RTO' THEN 'mce_rto'
                    WHEN p_platform = 'mce' AND vertical = 'CRC' THEN 'mce_crc'
                    ELSE p_platform
                END as platform
            FROM message_data
            WHERE version IS NOT NULL AND message IS NOT NULL
        )
        INSERT INTO subscriber.transaction_sms(phone, email, firstname, lastname, version, message, platform, campaign_date)
        SELECT 
            customer_phone,
            emailaddress,
            firstname,
            lastname,
            version,
            final_message,
            platform,
            CURRENT_DATE
        FROM final_messages;

        GET DIAGNOSTICS v_batch_count = ROW_COUNT;
        v_total_count := v_total_count + v_batch_count;

        -- Update cursor position for next batch
        IF v_batch_count > 0 THEN
            SELECT MAX(customer_phone) INTO v_last_processed_phone
            FROM (
                SELECT customer_phone
                FROM subscriber.subscriber_sms s
                LEFT JOIN subscriber.brand b ON s.brand_id = b.brand_id
                WHERE s.leaddate >= date_trunc('day', now()) + interval '1 hours'
                  AND s.leaddate <= date_trunc('day', now()) + interval '10 hours'
                  AND s.blacklisted <> 'true'
                  AND s.carrierparent NOT LIKE '%VERIZON%'
                  AND s.status <> 'not_mobile'
                  AND s.adid NOT LIKE 'CCS440811%'
                  AND s.original_adid NOT LIKE 'CCS440811%'
                  AND s.shorturl IS NOT NULL
                  AND s.customer_phone > v_last_processed_phone
                  AND NOT EXISTS (SELECT 1 FROM subscriber.optout_sms o WHERE o.phone = s.customer_phone)
                ORDER BY s.customer_phone
                LIMIT p_batch_size
            ) batch_phones;
        END IF;

        -- Log progress every batch (less verbose for day functions)
        IF v_batch_count > 0 THEN
            RAISE NOTICE 'Day NV batch: % records, Total: %, Time: %ms',
                v_batch_count, v_total_count,
                EXTRACT(MILLISECONDS FROM clock_timestamp() - v_batch_start_time);
        END IF;

        -- Exit if no more records or batch is smaller than expected
        EXIT WHEN v_batch_count < p_batch_size;

        -- Shorter delay for day functions (more frequent execution)
        PERFORM pg_sleep(0.05);
    END LOOP;

    -- Release the advisory lock
    PERFORM pg_advisory_unlock(v_lock_id);

    RETURN FORMAT('Day NV: Successfully inserted %s row(s) in %s seconds.',
                  v_total_count,
                  ROUND(EXTRACT(SECONDS FROM clock_timestamp() - v_start_time)::NUMERIC, 2));

EXCEPTION
    WHEN OTHERS THEN
        -- Make sure to release the lock even if an error occurs
        PERFORM pg_advisory_unlock(v_lock_id);
        RETURN CONCAT('Day NV Error: ', SQLERRM);
END;
$$
LANGUAGE plpgsql;
